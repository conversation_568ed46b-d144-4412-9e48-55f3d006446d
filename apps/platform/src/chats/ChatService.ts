/* eslint-disable indent */
import { WebSocketServer } from "ws";
import {
  Chat,
  Message,
  Agent,
  chatSchema,
  messageSchema,
  ChatEvent,
} from "./models/types";
import { logger } from "../config/logger";
import { checkIntegrationStatus } from "../integrations";
import * as agentsConfig from "../agents/agents.json";
import { Knex } from "knex";
import { getAvailableAgents } from "../integrations/IntegrationService";

export class ChatService {
  constructor(private db: Knex, private wss: WebSocketServer) {
    if (!db || !wss) {
      throw new Error("Database and WebSocket server are required");
    }
  }

  // Chat Operations
  async createChat(
    locationId: number,
    name: string,
    agent?: Agent
  ): Promise<Chat> {
    const timestamp = new Date();
    // Generate a unique chat_id (UUID v4)
    const chat_id = require("crypto").randomUUID();

    const agentIds = agent ? [agent.id] : [];
    const agentIdsJson = JSON.stringify(agentIds);

    // Log for debugging the production issue
    logger.info({
      message: "Creating chat with agent_ids",
      locationId,
      name,
      agentId: agent?.id,
      agentIds,
      agentIdsJson,
      chat_id
    });

    let id: number;
    let chat: any;

    try {
      [id] = await this.db("chats").insert({
        chat_id,
        name,
        location_id: locationId,
        agent_ids: agentIdsJson,
        status: "active",
        created_at: timestamp,
        updated_at: timestamp,
        // Don't store redundant agent data
      });

      chat = await this.db("chats").where("id", id).first();
    } catch (error: any) {
      logger.error({
        message: "Error creating chat",
        error: error.message,
        sqlMessage: error.sqlMessage,
        locationId,
        name,
        agentId: agent?.id,
        agentIds,
        agentIdsJson,
        chat_id
      });
      throw error;
    }

    // Add the agents data by querying the agents table
    if (chat && agent) {
      chat.agents = [agent];
    } else {
      chat.agents = [];
    }

    // Broadcast chat creation event
    this.broadcastEvent({
      type: "created",
      chat_id: chat.id,
      location_id: locationId,
      timestamp: timestamp.toISOString(),
      data: chat,
    });

    return chat;
  }

  async getChat(chatId: string, locationId: number): Promise<Chat | null> {
    const chat = await this.db("chats")
      .where({
        chat_id: chatId,
        location_id: locationId,
      })
      .whereNot("status", "deleted")
      .first();

    if (!chat) return null;

    // Parse agent_ids from JSON if it's a string, otherwise handle as array
    let chatAgentIds: string[] = [];
    if (chat.agent_ids) {
      if (typeof chat.agent_ids === 'string') {
        try {
          chatAgentIds = JSON.parse(chat.agent_ids);
        } catch (e) {
          // If JSON parsing fails, treat as single value
          chatAgentIds = [chat.agent_ids];
        }
      } else if (Array.isArray(chat.agent_ids)) {
        chatAgentIds = chat.agent_ids;
      } else {
        // If it's neither string nor array, treat as single value
        chatAgentIds = [chat.agent_ids.toString()];
      }
    }

    // Get associated agents from the agent_ids array if it exists
    if (chatAgentIds.length > 0) {
      chat.agents = await this.db("agents").whereIn("id", chatAgentIds);
    } else {
      chat.agents = [];
    }

    return chat;
  }

  async listChats(locationId: number): Promise<Chat[]> {
    const chats = await this.db("chats")
      .where("location_id", locationId)
      .whereNot("status", "deleted")
      .orderBy("updated_at", "desc");

    // Get all unique agent IDs
    const agentIds = new Set<string>();
    chats.forEach((chat) => {
      // Parse agent_ids from JSON if it's a string, otherwise handle as array
      if (chat.agent_ids) {
        let chatAgentIds: string[] = [];
        if (typeof chat.agent_ids === 'string') {
          try {
            chatAgentIds = JSON.parse(chat.agent_ids);
          } catch (e) {
            // If JSON parsing fails, treat as single value
            chatAgentIds = [chat.agent_ids];
          }
        } else if (Array.isArray(chat.agent_ids)) {
          chatAgentIds = chat.agent_ids;
        } else {
          // If it's neither string nor array, treat as single value
          chatAgentIds = [chat.agent_ids.toString()];
        }

        chatAgentIds.forEach((id: string | number) =>
          agentIds.add(id.toString())
        );
      }
    });

    // Fetch all agents in one query for efficiency
    const agents =
      agentIds.size > 0
        ? await this.db("agents").whereIn("id", Array.from(agentIds))
        : [];

    // Map agents to their respective chats
    return chats.map((chat) => {
      // Parse agent_ids from JSON if it's a string, otherwise handle as array
      let chatAgentIds: string[] = [];
      if (chat.agent_ids) {
        if (typeof chat.agent_ids === 'string') {
          try {
            chatAgentIds = JSON.parse(chat.agent_ids);
          } catch (e) {
            // If JSON parsing fails, treat as single value
            chatAgentIds = [chat.agent_ids];
          }
        } else if (Array.isArray(chat.agent_ids)) {
          chatAgentIds = chat.agent_ids;
        } else {
          // If it's neither string nor array, treat as single value
          chatAgentIds = [chat.agent_ids.toString()];
        }
      }

      if (chatAgentIds.length > 0) {
        chat.agents = agents.filter((agent) => chatAgentIds.includes(agent.id));
      } else {
        chat.agents = [];
      }
      return chat;
    });
  }

  async renameChat(
    chatId: string,
    name: string,
    locationId: number
  ): Promise<void> {
    await this.db("chats")
      .where({
        chat_id: chatId,
        location_id: locationId,
      })
      .update({
        name,
        updated_at: new Date(),
      });

    const chat = await this.getChat(chatId, locationId);
    if (chat) {
      this.broadcastEvent({
        type: "updated",
        chat_id: chat.id,
        location_id: locationId,
        timestamp: new Date().toISOString(),
        data: chat,
      });
    }
  }

  async archiveChat(chatId: string, locationId: number): Promise<void> {
    await this.db("chats")
      .where({
        chat_id: chatId,
        location_id: locationId,
      })
      .update({
        status: "archived",
        updated_at: new Date(),
      });

    this.broadcastEvent({
      type: "updated",
      chat_id: parseInt(chatId, 10),
      location_id: locationId,
      timestamp: new Date().toISOString(),
      data: { status: "archived" },
    });
  }

  async deleteChat(chatId: string, locationId: number): Promise<void> {
    await this.db("chats")
      .where({
        chat_id: chatId,
        location_id: locationId,
      })
      .update({
        status: "deleted",
        updated_at: new Date(),
      });

    this.broadcastEvent({
      type: "deleted",
      chat_id: parseInt(chatId, 10),
      location_id: locationId,
      timestamp: new Date().toISOString(),
      data: { id: chatId },
    });
  }

  // Message Operations
  async createMessage(message: Omit<Message, "id">): Promise<Message> {
    const [id] = await this.db("messages").insert(message);
    const createdMessage = await this.db("messages").where("id", id).first();

    // Broadcast message creation event
    this.broadcastEvent({
      type: "message",
      chat_id: parseInt(message.chat_id as string, 10),
      location_id: await this.getChatLocationId(message.chat_id),
      timestamp: message.timestamp.toISOString(),
      data: createdMessage,
    });

    return createdMessage;
  }

  async getMessages(
    chatId: string,
    limit = 50,
    before?: Date
  ): Promise<Message[]> {
    let query = this.db("messages")
      .where("chat_id", chatId)
      .orderBy("timestamp", "desc")
      .limit(limit);

    if (before) {
      query = query.where("timestamp", "<", before);
    }

    return await query;
  }

  async getMessageById(messageId: number): Promise<Message | null> {
    return (await this.db("messages").where("id", messageId).first()) || null;
  }

  /**
   * Create a system message for document processing notifications
   */
  async createDocumentProcessingMessage(
    chatId: string,
    fileName: string,
    status: "processing" | "completed" | "failed",
    estimatedTime?: string
  ): Promise<Message> {
    let content: string;

    switch (status) {
      case "processing":
        content = `📄 I've received your document "${fileName}" and I'm currently analyzing it. ${
          estimatedTime ? `This usually takes ${estimatedTime}.` : "This usually takes 3-5 minutes."
        } I'll let you know when I'm ready to answer questions about it!`;
        break;
      case "completed":
        content = `✅ Great! I've finished analyzing "${fileName}" and I'm now ready to answer questions about it. Feel free to ask me anything about the document!`;
        break;
      case "failed":
        content = `❌ I encountered an issue while processing "${fileName}". Please try re-uploading the document or contact support if the problem persists.`;
        break;
    }

    const message: Omit<Message, "id"> = {
      chat_id: chatId,
      role: "assistant",
      content,
      timestamp: new Date(),
      metadata: JSON.stringify({
        type: "document_processing_notification",
        document_name: fileName,
        status,
        estimated_time: estimatedTime
      })
    };

    return await this.createMessage(message);
  }

  // WebSocket Event Handling
  private broadcastEvent(event: ChatEvent) {
    const message = JSON.stringify(event);
    this.wss.clients.forEach((client: any) => {
      if (client.locationId === event.location_id) {
        client.send(message);
      }
    });
  }

  private async getChatLocationId(chatId: string): Promise<number> {
    const chat = await this.db("chats")
      .where("chat_id", chatId)
      .select("location_id")
      .first();
    return chat?.location_id;
  }

  // Agent Operations
  async getAvailableAgents(locationId: number): Promise<Agent[]> {
    const agentResults = await getAvailableAgents(
      this.db,
      locationId.toString()
    );

    // Convert to Agent format and filter to only unlocked agents
    return agentResults
      .filter((result) => result.unlocked)
      .map((result) => ({
        id: result.id,
        name: result.name,
        role: result.role,
        description: result.description,
        capabilities: result.capabilities,
        icon: result.icon,
        disabled: result.disabled,
        metadata: result.metadata,
      }));
  }

  async getAgents(): Promise<Agent[]> {
    return await this.db("agents")
      .where({ disabled: false })
      .orWhereNull("disabled")
      .orderBy("name");
  }

  async getAgentById(id: string): Promise<Agent | null> {
    return (await this.db("agents").where({ id }).first()) || null;
  }

  /**
   * Updates the metadata of a chat
   */
  async updateChatMetadata(
    chatId: string,
    metadata: Record<string, any>
  ): Promise<void> {
    // Get the current chat to access its metadata
    const chat = await this.getChat(
      chatId,
      await this.getChatLocationId(chatId)
    );
    if (!chat) {
      throw new Error(`Chat not found: ${chatId}`);
    }

    // Merge the existing metadata with the new metadata
    const updatedMetadata = {
      ...(chat.metadata || {}),
      ...metadata,
    };

    // Update using a properly formatted JSON object for the metadata column
    await this.db("chats")
      .where("chat_id", chatId)
      .update({
        metadata: JSON.stringify(updatedMetadata),
        updated_at: new Date(),
      });

    // Broadcast update event
    this.broadcastEvent({
      type: "updated",
      chat_id: parseInt(chatId, 10),
      location_id: await this.getChatLocationId(chatId),
      timestamp: new Date().toISOString(),
      data: { metadata: updatedMetadata },
    });
  }
}
