import { Job } from "../queue";
import { Document } from "./Document";
import { DocumentVectorService } from "./DocumentVectorService";
import { logger } from "../config/logger";
import { OnboardingJobTracker } from "../locations/OnboardingJobTracker";

interface DocumentVectorParams {
  document_id: number;
  location_id: number;
  retry_count?: number;
}

export default class DocumentVectorJob extends Job {
  static $name = "document-vector-job";

  document_id!: number;
  location_id!: number;
  retry_count?: number;

  static from(params: DocumentVectorParams) {
    logger.info(`Creating document vectorization job`, {
      document_id: params.document_id,
      location_id: params.location_id,
      retry_count: params.retry_count || 0,
    });

    return new this({
      document_id: params.document_id,
      location_id: params.location_id,
      retry_count: params.retry_count || 0,
    });
  }

  static async handler({
    document_id,
    location_id,
    retry_count = 0,
  }: DocumentVectorParams) {
    logger.info(
      `Starting document vectorization for document ${document_id} in location ${location_id}`
    );

    const MAX_RETRIES = 3;
    const jobType = "document_vectorization";

    try {
      // Initialize the vector service
      await DocumentVectorService.initialize();

      // Start tracking this job
      OnboardingJobTracker.startJob(location_id, jobType);

      // Get the document
      const document = await Document.first((qb) =>
        qb.where({ id: document_id, location_id })
      );

      if (!document) {
        throw new Error(`Document ${document_id} not found`);
      }

      // Process the document
      await DocumentVectorService.processDocument(document_id, location_id);

      // Mark job as complete
      OnboardingJobTracker.completeJob(location_id, jobType);

      logger.info(
        `Document vectorization completed successfully for document ${document_id}`
      );

      // Send completion notification if chat_id is available
      try {
        const chatId = document.metadata?.chat_id;
        if (chatId) {
          logger.info(`Sending completion notification to chat ${chatId} for document ${document.name}`);
          const { ChatService } = await import("../chats/ChatService");
          const chatService = new ChatService(App.main.db, App.main.wss);

          await chatService.createDocumentProcessingMessage(
            chatId,
            document.name,
            "completed"
          );
          logger.debug(`Completion notification sent to chat ${chatId}`);
        }
      } catch (error) {
        logger.warn(`Failed to send completion notification: ${error instanceof Error ? error.message : "Unknown error"}`);
        // Don't fail the job if notification fails
      }

      return true;
    } catch (error) {
      logger.error(
        `Document vectorization failed for document ${document_id}: ${
          error instanceof Error ? error.message : "Unknown error"
        }`,
        error
      );

      // If we haven't exceeded max retries, try again
      if (retry_count < MAX_RETRIES) {
        logger.info(
          `Retrying document vectorization for document ${document_id} (attempt ${
            retry_count + 1
          } of ${MAX_RETRIES})`
        );

        // Queue a retry with exponential backoff
        const backoff = Math.pow(2, retry_count) * 1000; // 1s, 2s, 4s...

        // Calculate priority based on document age (same logic as analysis job)
        const now = Date.now();
        const uploadTime = document.created_at ? new Date(document.created_at).getTime() : now;
        const ageMinutes = (now - uploadTime) / (1000 * 60);

        let priority = 1; // Default priority
        if (ageMinutes <= 5) {
          priority = 10; // Very recent uploads
        } else if (ageMinutes <= 60) {
          priority = 7; // Recent uploads
        } else if (ageMinutes <= 1440) { // 24 hours
          priority = 5; // Same day uploads
        }

        await DocumentVectorJob.from({
          document_id,
          location_id,
          retry_count: retry_count + 1,
        })
          .delay(backoff)
          .priority(priority)
          .queue();
      } else {
        // Max retries exceeded, mark job as failed
        OnboardingJobTracker.failJob(
          location_id,
          jobType,
          error instanceof Error ? error : new Error(String(error))
        );

        // Send failure notification if chat_id is available
        try {
          const chatId = document.metadata?.chat_id;
          if (chatId) {
            logger.info(`Sending failure notification to chat ${chatId} for document ${document.name}`);
            const { ChatService } = await import("../chats/ChatService");
            const chatService = new ChatService(App.main.db, App.main.wss);

            await chatService.createDocumentProcessingMessage(
              chatId,
              document.name,
              "failed"
            );
            logger.debug(`Failure notification sent to chat ${chatId}`);
          }
        } catch (notificationError) {
          logger.warn(`Failed to send failure notification: ${notificationError instanceof Error ? notificationError.message : "Unknown error"}`);
          // Don't fail the job if notification fails
        }
      }

      return false;
    }
  }
}
